<script lang="ts" module>
	import { slide } from 'svelte/transition';
	import type { Tree } from '../custom/customTreeFromTable';
	import type { TreeItemAdv } from './treeTypes';

	export { treeItemView as TreeItemViewAdvanced };
</script>

{#snippet treeItemView(item: TreeItemAdv, treeState: Tree<TreeItemAdv>)}
	<!-- svelte-ignore a11y_click_events_have_key_events -->
	<!-- svelte-ignore a11y_no_static_element_interactions -->
	<!-- svelte-ignore event_directive_deprecated -->
	<div
		class="ti relative"
		class:selected={item.selected}
		transition:slide|global
		on:click={() => {
			if (treeState.selectedItem) {
				treeState.selectedItem.selected = false;
			}
			treeState.selectedItem = item;
			item.selected = true;
			item.expanded = !item.expanded;
		}}
	>
		<img src="/images/icons/{item.icon}.svg" alt={item.icon} class="h-4 w-4" />
		<span>{item.title}</span>

		{#if item.visibleCount}
			<span
				class="me-2 rounded-sm bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-300"
				>{item.visibleCount ? item.visibleCount : ''}</span
			>
		{/if}
		<span class="ml-auto"></span>
		<span
			class="me-2 rounded-sm bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-300"
			>{item.childrenCount ? item.childrenCount : ''}</span
		>
		{#if item.childrenCount}
			<span>∣</span>
			<span
				class="me-2 rounded-sm bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300"
				>{item.leafCount ? item.leafCount : ''}</span
			>
		{/if}
	</div>
{/snippet}

<style>
	.ti {
		display: flex;
		gap: 0.5rem;
		padding: var(--var-padding);
		align-items: center;
	}

	.selected {
		background-color: #a46233;
		color: white;
		border-radius: 0.75rem;
	}
</style>
