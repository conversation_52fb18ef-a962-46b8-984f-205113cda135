<script lang="ts">
	import Filters from '$lib/nel/Filters.svelte';
	import { filterData, type FilterCriteria } from '$lib/nel/filters/filterData';
	import { buildTreeFromTable } from '$lib/nel/treeDataSet';
	import { getData } from './data.remote';
	import MyTreeView from '../lib/generic/tree/custom/MyTreeView.svelte';
	import { TreeItemViewAdvanced } from '$lib/generic/tree/advanced/TreeItemViewSnippet.svelte';

	let dataSet = await getData();

	let tree = buildTreeFromTable(dataSet);
	let treeState = $state(tree);

	function onFilterChanged(filterCriteria: FilterCriteria) {
		filterData(treeState, filterCriteria);
	}

	let compact = $state(false);
</script>

<div class="flex flex-1 gap-4 overflow-hidden">
	<div class="w-2/3 flex-1 overflow-y-auto">
		<MyTreeView
			treeData={treeState}
			treeItemView={TreeItemViewAdvanced}
			--var-padding={compact ? 0 : '0.5rem'}
		/>
	</div>
	<div class="flex-1 overflow-y-auto">
		<Filters data={dataSet} onSelection={onFilterChanged} />
	</div>
</div>
