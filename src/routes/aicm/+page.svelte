<script lang="ts">
	import { TreeItemViewAdvanced } from '$lib/generic/tree/advanced/TreeItemViewSnippet.svelte';
	import MyTreeView from '../../lib/generic/tree/custom/MyTreeView.svelte';
	import { getData } from './data.remote';
	import { buildTreeFromTable } from './treeDataSet';

	const dataSet = await getData();

	const tree = buildTreeFromTable(dataSet);
	let treeState = $state(tree);

	let compact = $state(false);
</script>

<p class="text-center text-xl">Asset Classification</p>

<div class="flex flex-1 gap-4 overflow-hidden">
	<div class="w-2/3 flex-1 overflow-y-auto">
		<MyTreeView
			treeData={treeState}
			treeItemView={TreeItemViewAdvanced}
			--var-padding={compact ? 0 : '0.5rem'}
		/>
	</div>
	<div class="flex-1 overflow-y-auto">
		<!-- <Filters data={dataSet} onSelection={onFilterChanged} /> -->
	</div>
</div>
